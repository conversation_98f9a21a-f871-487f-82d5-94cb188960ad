<div class="loan-search-container" [class.searching]="isSearching">
  <!-- Application Title -->
  <div class="app-title">
    <h1>
      <span class="letter" data-letter="E">0</span>
      <span class="letter" data-letter="n">1</span>
      <span class="letter" data-letter="s">2</span>
      <span class="letter" data-letter="e">3</span>
      <span class="letter" data-letter="m">4</span>
      <span class="letter" data-letter="b">5</span>
      <span class="letter" data-letter="l">6</span>
      <span class="letter" data-letter="e">7</span>
    </h1>
  </div>

  <!-- Centered Unified Search Form -->
  <div class="search-form">
    <div class="search-input-container">
      <ens-animated-loan-input
        placeholder="Enter 10-digit loan number"
        [maxLength]="10"
        [showPlaceholdersAfter]="1"
        (valueChange)="onAnimatedInputChange($event)"
        (enterPressed)="onAnimatedInputEnter($event)">
      </ens-animated-loan-input>

      <!-- Reserved space for error/warning messages -->
      <div class="message-area">
        <div *ngIf="searchError" class="search-error">
          <mat-icon>error</mat-icon>
          <span>{{ searchError }}</span>
        </div>
        <div *ngIf="isSearching" class="search-loading">
          <span>Searching...</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Searches -->
  <div *ngIf="recentSearches.length > 0" class="recent-searches">
    <div class="recent-searches-header">
      <mat-icon>history</mat-icon>
      <span>Recent Searches</span>
      <button
        mat-button
        (click)="clearRecentSearches()"
        class="clear-all-button">
        <mat-icon>clear_all</mat-icon>
        Clear All
      </button>
    </div>

    <div class="recent-search-item"
         *ngFor="let loanNumber of recentSearches; trackBy: trackByLoanNumber"
         (click)="selectRecentSearch(loanNumber)">
      <mat-icon>search</mat-icon>
      <span class="loan-number">{{ formatLoanNumber(loanNumber) }}</span>
      <button
        mat-icon-button
        class="remove-button"
        (click)="removeFromRecent(loanNumber, $event)">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- System Status Carousel -->
  <ens-system-status-carousel></ens-system-status-carousel>
</div>
