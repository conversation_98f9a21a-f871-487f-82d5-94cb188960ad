.system-status-carousel {
  width: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 24px;
  position: relative;
  overflow: hidden;

  .carousel-header {
    margin-bottom: 16px;
    
    .header-content {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .status-icon {
        color: #28a745;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
      
      .carousel-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #212529;
      }
      
      .status-summary {
        margin-left: auto;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        color: #6c757d;
        
        .operational-count {
          color: #28a745;
          font-weight: 500;
        }
        
        .issues-count {
          color: #dc3545;
          font-weight: 500;
        }
        
        .all-good {
          color: #28a745;
          font-weight: 500;
        }
        
        .divider {
          color: #adb5bd;
        }
      }
    }
  }

  .carousel-container {
    overflow: hidden;
    border-radius: 8px;
    
    .carousel-track {
      display: flex;
      transition: transform 0.5s ease-in-out;
      gap: 16px;
      
      .system-card {
        flex: 0 0 calc(33.333% - 11px); // Show 3 items, account for gap
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #28a745;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &.status-operational {
          border-left-color: #28a745;
        }
        
        &.status-degraded {
          border-left-color: #ffc107;
        }
        
        &.status-outage {
          border-left-color: #dc3545;
        }
        
        &.status-maintenance {
          border-left-color: #6f42c1;
        }
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .system-info {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .system-status-icon {
              font-size: 20px;
              width: 20px;
              height: 20px;
              
              &.status-operational {
                color: #28a745;
              }
              
              &.status-degraded {
                color: #ffc107;
              }
              
              &.status-outage {
                color: #dc3545;
              }
              
              &.status-maintenance {
                color: #6f42c1;
              }
            }
            
            .system-name {
              font-weight: 600;
              font-size: 16px;
              color: #212529;
            }
          }
          
          .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            
            &.status-operational {
              background-color: #d4edda;
              color: #155724;
            }
            
            &.status-degraded {
              background-color: #fff3cd;
              color: #856404;
            }
            
            &.status-outage {
              background-color: #f8d7da;
              color: #721c24;
            }
            
            &.status-maintenance {
              background-color: #e2d9f3;
              color: #4a2c6a;
            }
          }
        }
        
        .card-content {
          .system-description {
            font-size: 14px;
            color: #6c757d;
            margin: 0 0 12px 0;
            line-height: 1.4;
          }
          
          .metrics {
            display: flex;
            gap: 16px;
            margin-bottom: 12px;
            
            .metric {
              display: flex;
              flex-direction: column;
              gap: 2px;
              
              .metric-label {
                font-size: 12px;
                color: #adb5bd;
                text-transform: uppercase;
                font-weight: 500;
              }
              
              .metric-value {
                font-size: 14px;
                color: #495057;
                font-weight: 600;
              }
            }
          }
          
          .last-updated {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #adb5bd;
            
            .update-icon {
              font-size: 14px;
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
  }

  .carousel-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
    
    .control-btn {
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
      }
      
      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        color: #6c757d;
      }
    }
    
    .indicators {
      display: flex;
      gap: 6px;
      
      .indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #dee2e6;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &.active {
          background: #007bff;
        }
        
        &:hover {
          background: #adb5bd;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .system-status-carousel {
    .carousel-container .carousel-track .system-card {
      flex: 0 0 calc(50% - 8px); // Show 2 items on tablets
    }
  }
}

@media (max-width: 480px) {
  .system-status-carousel {
    padding: 16px;
    
    .carousel-header .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      
      .status-summary {
        margin-left: 0;
      }
    }
    
    .carousel-container .carousel-track .system-card {
      flex: 0 0 100%; // Show 1 item on mobile
    }
  }
}
